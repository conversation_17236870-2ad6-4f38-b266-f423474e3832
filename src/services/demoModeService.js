/**
 * Demo Mode Service
 * Provides comprehensive demo data for all app features
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { getDemoCredentials, isDemoMode } from './authService';

// Check if current user is in demo mode
export const isCurrentUserInDemoMode = async () => {
  try {
    const userData = await AsyncStorage.getItem('userData');
    if (userData) {
      const parsedData = JSON.parse(userData);
      return isDemoMode(parsedData);
    }
    return false;
  } catch (error) {
    console.error('Error checking demo mode:', error);
    return false;
  }
};

// Demo timetable data
export const getDemoTimetableData = (userType) => {
  if (userType === 'teacher') {
    return {
      success: true,
      total_branches: 3,
      branches: [
        {
          branch_id: 1,
          branch_name: 'Main Campus',
          timetable: [
            // Monday
            {
              timetable_id: 1,
              subject_name: 'Mathematics',
              grade_name: 'Grade 10A',
              time: '08:00-09:00',
              day: 'Monday',
              week_day: 1,
              week_time: 1,
              room: 'Room 101',
              attendance_taken: true,
              student_count: 28,
            },
            {
              timetable_id: 2,
              subject_name: 'Physics',
              grade_name: 'Grade 11B',
              time: '09:15-10:15',
              day: 'Monday',
              week_day: 1,
              week_time: 2,
              room: 'Lab 201',
              attendance_taken: false,
              student_count: 24,
            },
            {
              timetable_id: 3,
              subject_name: 'Mathematics',
              grade_name: 'Grade 9A',
              time: '10:30-11:30',
              day: 'Monday',
              week_day: 1,
              week_time: 3,
              room: 'Room 101',
              attendance_taken: true,
              student_count: 26,
            },
            {
              timetable_id: 4,
              subject_name: 'Advanced Mathematics',
              grade_name: 'Grade 12A',
              time: '13:30-14:30',
              day: 'Monday',
              week_day: 1,
              week_time: 6,
              room: 'Room 101',
              attendance_taken: false,
              student_count: 22,
            },
            // Tuesday
            {
              timetable_id: 5,
              subject_name: 'Mathematics',
              grade_name: 'Grade 10A',
              time: '08:00-09:00',
              day: 'Tuesday',
              week_day: 2,
              week_time: 1,
              room: 'Room 101',
              attendance_taken: true,
              student_count: 28,
            },
            {
              timetable_id: 6,
              subject_name: 'Physics',
              grade_name: 'Grade 11A',
              time: '09:15-10:15',
              day: 'Tuesday',
              week_day: 2,
              week_time: 2,
              room: 'Lab 201',
              attendance_taken: true,
              student_count: 25,
            },
            {
              timetable_id: 7,
              subject_name: 'Mathematics',
              grade_name: 'Grade 8B',
              time: '11:45-12:45',
              day: 'Tuesday',
              week_day: 2,
              week_time: 4,
              room: 'Room 101',
              attendance_taken: false,
              student_count: 30,
            },
            // Wednesday
            {
              timetable_id: 8,
              subject_name: 'Advanced Mathematics',
              grade_name: 'Grade 12A',
              time: '08:00-09:00',
              day: 'Wednesday',
              week_day: 3,
              week_time: 1,
              room: 'Room 101',
              attendance_taken: true,
              student_count: 22,
            },
            {
              timetable_id: 9,
              subject_name: 'Physics',
              grade_name: 'Grade 11B',
              time: '10:30-11:30',
              day: 'Wednesday',
              week_day: 3,
              week_time: 3,
              room: 'Lab 201',
              attendance_taken: false,
              student_count: 24,
            },
            // Thursday
            {
              timetable_id: 10,
              subject_name: 'Mathematics',
              grade_name: 'Grade 9A',
              time: '09:15-10:15',
              day: 'Thursday',
              week_day: 4,
              week_time: 2,
              room: 'Room 101',
              attendance_taken: true,
              student_count: 26,
            },
            {
              timetable_id: 11,
              subject_name: 'Physics',
              grade_name: 'Grade 11A',
              time: '13:30-14:30',
              day: 'Thursday',
              week_day: 4,
              week_time: 6,
              room: 'Lab 201',
              attendance_taken: false,
              student_count: 25,
            },
            // Friday
            {
              timetable_id: 12,
              subject_name: 'Mathematics',
              grade_name: 'Grade 10A',
              time: '08:00-09:00',
              day: 'Friday',
              week_day: 5,
              week_time: 1,
              room: 'Room 101',
              attendance_taken: false,
              student_count: 28,
            },
            {
              timetable_id: 13,
              subject_name: 'Mathematics',
              grade_name: 'Grade 8B',
              time: '10:30-11:30',
              day: 'Friday',
              week_day: 5,
              week_time: 3,
              room: 'Room 101',
              attendance_taken: true,
              student_count: 30,
            },
          ],
        },
        {
          branch_id: 2,
          branch_name: 'Secondary Campus',
          timetable: [
            {
              timetable_id: 14,
              subject_name: 'Mathematics',
              grade_name: 'Grade 9C',
              time: '08:00-09:00',
              day: 'Monday',
              week_day: 1,
              week_time: 1,
              room: 'Room 305',
              attendance_taken: false,
              student_count: 23,
            },
            {
              timetable_id: 15,
              subject_name: 'Mathematics',
              grade_name: 'Grade 7A',
              time: '10:30-11:30',
              day: 'Tuesday',
              week_day: 2,
              week_time: 3,
              room: 'Room 305',
              attendance_taken: true,
              student_count: 27,
            },
            {
              timetable_id: 16,
              subject_name: 'Mathematics',
              grade_name: 'Grade 9C',
              time: '13:30-14:30',
              day: 'Wednesday',
              week_day: 3,
              week_time: 6,
              room: 'Room 305',
              attendance_taken: false,
              student_count: 23,
            },
            {
              timetable_id: 17,
              subject_name: 'Mathematics',
              grade_name: 'Grade 7A',
              time: '09:15-10:15',
              day: 'Thursday',
              week_day: 4,
              week_time: 2,
              room: 'Room 305',
              attendance_taken: true,
              student_count: 27,
            },
            {
              timetable_id: 18,
              subject_name: 'Mathematics',
              grade_name: 'Grade 8A',
              time: '11:45-12:45',
              day: 'Friday',
              week_day: 5,
              week_time: 4,
              room: 'Room 305',
              attendance_taken: false,
              student_count: 25,
            },
          ],
        },
        {
          branch_id: 3,
          branch_name: 'International Campus',
          timetable: [
            {
              timetable_id: 19,
              subject_name: 'Mathematics',
              grade_name: 'IB Math HL',
              time: '09:15-10:15',
              day: 'Monday',
              week_day: 1,
              week_time: 2,
              room: 'Room 401',
              attendance_taken: true,
              student_count: 18,
            },
            {
              timetable_id: 20,
              subject_name: 'Mathematics',
              grade_name: 'IB Math SL',
              time: '13:30-14:30',
              day: 'Tuesday',
              week_day: 2,
              week_time: 6,
              room: 'Room 401',
              attendance_taken: false,
              student_count: 20,
            },
            {
              timetable_id: 21,
              subject_name: 'Mathematics',
              grade_name: 'IB Math HL',
              time: '10:30-11:30',
              day: 'Thursday',
              week_day: 4,
              week_time: 3,
              room: 'Room 401',
              attendance_taken: true,
              student_count: 18,
            },
          ],
        },
      ],
    };
  } else {
    // Student timetable
    return {
      success: true,
      Monday: [
        {
          time: '08:00-09:00',
          subject: 'Mathematics',
          teacher: 'Sarah Johnson',
          room: 'Room 101',
          subject_code: 'MATH10A',
          type: 'Core',
          period: 1,
        },
        {
          time: '09:15-10:15',
          subject: 'English Literature',
          teacher: 'John Smith',
          room: 'Room 102',
          subject_code: 'ENG10A',
          type: 'Core',
          period: 2,
        },
        {
          time: '10:30-11:30',
          subject: 'Physics',
          teacher: 'Dr. Wilson',
          room: 'Lab 201',
          subject_code: 'PHY10A',
          type: 'Science',
          period: 3,
        },
        {
          time: '11:45-12:45',
          subject: 'World History',
          teacher: 'Ms. Brown',
          room: 'Room 103',
          subject_code: 'HIST10A',
          type: 'Humanities',
          period: 4,
        },
        {
          time: '13:30-14:30',
          subject: 'Physical Education',
          teacher: 'Coach Davis',
          room: 'Gymnasium',
          subject_code: 'PE10A',
          type: 'Physical',
          period: 5,
        },
        {
          time: '14:45-15:45',
          subject: 'Study Period',
          teacher: 'Homeroom Teacher',
          room: 'Room 101',
          subject_code: 'STUDY',
          type: 'Study',
        },
      ],
      Tuesday: [
        {
          time: '08:00-09:00',
          subject: 'Biology',
          teacher: 'Dr. Green',
          room: 'Lab 301',
          subject_code: 'BIO10A',
          type: 'Science',
          period: 1,
        },
        {
          time: '09:15-10:15',
          subject: 'Chemistry',
          teacher: 'Prof. White',
          room: 'Lab 302',
          subject_code: 'CHEM10A',
          type: 'Science',
          period: 2,
        },
        {
          time: '10:30-11:30',
          subject: 'Geography',
          teacher: 'Ms. Taylor',
          room: 'Room 104',
          subject_code: 'GEO10A',
          type: 'Social Studies',
          period: 3,
        },
        {
          time: '11:45-12:45',
          subject: 'Art',
          teacher: 'Mr. Clark',
          room: 'Art Studio',
          subject_code: 'ART10A',
          type: 'Creative',
          period: 4,
        },
        {
          time: '13:30-14:30',
          subject: 'Music',
          teacher: 'Ms. Adams',
          room: 'Music Room',
          subject_code: 'MUS10A',
          type: 'Creative',
          period: 5,
        },
      ],
      Wednesday: [
        {
          time: '08:00-09:00',
          subject: 'Mathematics',
          teacher: 'Sarah Johnson',
          room: 'Room 101',
        },
        {
          time: '09:15-10:15',
          subject: 'Literature',
          teacher: 'John Smith',
          room: 'Room 102',
        },
        {
          time: '10:30-11:30',
          subject: 'Computer Science',
          teacher: 'Mr. Tech',
          room: 'Computer Lab',
        },
        {
          time: '11:45-12:45',
          subject: 'Economics',
          teacher: 'Dr. Money',
          room: 'Room 105',
        },
      ],
      Thursday: [
        {
          time: '08:00-09:00',
          subject: 'Physics',
          teacher: 'Dr. Wilson',
          room: 'Lab 201',
        },
        {
          time: '09:15-10:15',
          subject: 'Chemistry',
          teacher: 'Prof. White',
          room: 'Lab 302',
        },
        {
          time: '10:30-11:30',
          subject: 'Mathematics',
          teacher: 'Sarah Johnson',
          room: 'Room 101',
        },
        {
          time: '11:45-12:45',
          subject: 'Psychology',
          teacher: 'Dr. Mind',
          room: 'Room 106',
        },
      ],
      Friday: [
        {
          time: '08:00-09:00',
          subject: 'English',
          teacher: 'John Smith',
          room: 'Room 102',
        },
        {
          time: '09:15-10:15',
          subject: 'Biology',
          teacher: 'Dr. Green',
          room: 'Lab 301',
        },
        {
          time: '10:30-11:30',
          subject: 'PE',
          teacher: 'Coach Davis',
          room: 'Gymnasium',
        },
        {
          time: '11:45-12:45',
          subject: 'Study Hall',
          teacher: 'Various',
          room: 'Library',
        },
      ],
    };
  }
};

// Demo BPS (Behavior Point System) data
export const getDemoBPSData = (userType) => {
  if (userType === 'teacher') {
    return {
      success: true,
      branches: [
        {
          branch_id: 1,
          branch_name: 'Main Campus',
          total_students: 180,
          bps_records: [
            {
              id: 1,
              discipline_record_id: 1,
              student_id: 1,
              student_name: 'Alex Chen',
              item_title: 'Excellent Homework Submission',
              item_type: 'PRS',
              item_point: 3,
              date: '2024-01-15',
              teacher_name: 'Sarah Johnson',
              note: 'Outstanding work on algebra problems',
              classroom_name: 'Grade 10A',
            },
            {
              id: 2,
              discipline_record_id: 2,
              student_id: 2,
              student_name: 'Emma Wilson',
              item_title: 'Active Class Participation',
              item_type: 'PRS',
              item_point: 2,
              date: '2024-01-14',
              teacher_name: 'Sarah Johnson',
              note: 'Excellent questions and engagement',
              classroom_name: 'Grade 10A',
            },
            {
              id: 3,
              discipline_record_id: 3,
              student_id: 3,
              student_name: 'Michael Rodriguez',
              item_title: 'Leadership in Group Project',
              item_type: 'PRS',
              item_point: 3,
              date: '2024-01-13',
              teacher_name: 'Mr. Tech',
              note: 'Excellent leadership in computer science project',
              classroom_name: 'Grade 10A',
            },
            {
              id: 4,
              discipline_record_id: 4,
              student_id: 4,
              student_name: 'Sophia Kim',
              item_title: 'Disruptive Behavior',
              item_type: 'DPS',
              item_point: -2,
              date: '2024-01-11',
              teacher_name: 'Prof. White',
              note: 'Talking during chemistry lecture',
              classroom_name: 'Grade 10A',
            },
            {
              id: 5,
              discipline_record_id: 5,
              student_id: 6,
              student_name: 'Isabella Garcia',
              item_title: 'Outstanding Science Fair Project',
              item_type: 'PRS',
              item_point: 5,
              date: '2024-01-15',
              teacher_name: 'Dr. Green',
              note: 'Exceptional research on renewable energy',
              classroom_name: 'Grade 11B',
            },
          ],
          classes: [
            {
              class_id: 1,
              class_name: 'Grade 10A',
              student_count: 28,
              students: [
                {
                  student_id: 1,
                  student_name: 'Alex Chen',
                  total_points: 18,
                  recent_records: [
                    {
                      id: 1,
                      item_title: 'Excellent Homework Submission',
                      item_type: 'PRS',
                      item_point: 3,
                      date: '2024-01-15',
                      teacher_name: 'Sarah Johnson',
                      note: 'Outstanding work on algebra problems',
                    },
                    {
                      id: 2,
                      item_title: 'Helping Classmates',
                      item_type: 'PRS',
                      item_point: 2,
                      date: '2024-01-12',
                      teacher_name: 'Dr. Wilson',
                      note: 'Assisted struggling students during physics lab',
                    },
                  ],
                },
                {
                  student_id: 2,
                  student_name: 'Emma Wilson',
                  total_points: 15,
                  recent_records: [
                    {
                      id: 3,
                      item_title: 'Active Class Participation',
                      item_type: 'PRS',
                      item_point: 2,
                      date: '2024-01-14',
                      teacher_name: 'Sarah Johnson',
                      note: 'Excellent questions and engagement',
                    },
                    {
                      id: 4,
                      item_title: 'Late Assignment',
                      item_type: 'DPS',
                      item_point: -1,
                      date: '2024-01-10',
                      teacher_name: 'Ms. Brown',
                      note: 'History essay submitted 1 day late',
                    },
                  ],
                },
                {
                  student_id: 3,
                  student_name: 'Michael Rodriguez',
                  total_points: 22,
                  recent_records: [
                    {
                      id: 5,
                      item_title: 'Leadership in Group Project',
                      item_type: 'PRS',
                      item_point: 3,
                      date: '2024-01-13',
                      teacher_name: 'Mr. Tech',
                      note: 'Excellent leadership in computer science project',
                    },
                  ],
                },
                {
                  student_id: 4,
                  student_name: 'Sophia Kim',
                  total_points: 8,
                  recent_records: [
                    {
                      id: 6,
                      item_title: 'Disruptive Behavior',
                      item_type: 'DPS',
                      item_point: -2,
                      date: '2024-01-11',
                      teacher_name: 'Prof. White',
                      note: 'Talking during chemistry lecture',
                    },
                    {
                      id: 7,
                      item_title: 'Improved Behavior',
                      item_type: 'PRS',
                      item_point: 2,
                      date: '2024-01-14',
                      teacher_name: 'Prof. White',
                      note: 'Much better focus and participation',
                    },
                  ],
                },
                {
                  student_id: 5,
                  student_name: 'James Thompson',
                  total_points: 12,
                  recent_records: [
                    {
                      id: 8,
                      item_title: 'Perfect Attendance Week',
                      item_type: 'PRS',
                      item_point: 2,
                      date: '2024-01-12',
                      teacher_name: 'Homeroom Teacher',
                      note: 'Perfect attendance for the week',
                    },
                  ],
                },
              ],
            },
            {
              class_id: 2,
              class_name: 'Grade 11B',
              student_count: 24,
              students: [
                {
                  student_id: 6,
                  student_name: 'Isabella Garcia',
                  total_points: 25,
                  recent_records: [
                    {
                      id: 9,
                      item_title: 'Outstanding Science Fair Project',
                      item_type: 'PRS',
                      item_point: 5,
                      date: '2024-01-15',
                      teacher_name: 'Dr. Green',
                      note: 'Exceptional research on renewable energy',
                    },
                  ],
                },
                {
                  student_id: 7,
                  student_name: 'David Park',
                  total_points: 6,
                  recent_records: [
                    {
                      id: 10,
                      item_title: 'Missing Assignment',
                      item_type: 'DPS',
                      item_point: -2,
                      date: '2024-01-13',
                      teacher_name: 'John Smith',
                      note: 'Failed to submit literature essay',
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          branch_id: 2,
          branch_name: 'Secondary Campus',
          total_students: 120,
          bps_records: [
            {
              id: 6,
              discipline_record_id: 6,
              student_id: 8,
              student_name: 'Olivia Johnson',
              item_title: 'Excellent Test Score',
              item_type: 'PRS',
              item_point: 3,
              date: '2024-01-14',
              teacher_name: 'Math Teacher',
              note: '95% on algebra test',
              classroom_name: 'Grade 9C',
            },
            {
              id: 7,
              discipline_record_id: 7,
              student_id: 9,
              student_name: 'Lucas Brown',
              item_title: 'Helping Younger Students',
              item_type: 'PRS',
              item_point: 2,
              date: '2024-01-13',
              teacher_name: 'Homeroom Teacher',
              note: 'Mentoring Grade 7 students during lunch',
              classroom_name: 'Grade 9C',
            },
          ],
          classes: [
            {
              class_id: 3,
              class_name: 'Grade 9C',
              student_count: 23,
              students: [
                {
                  student_id: 8,
                  student_name: 'Olivia Johnson',
                  total_points: 14,
                  recent_records: [
                    {
                      id: 11,
                      item_title: 'Excellent Test Score',
                      item_type: 'PRS',
                      item_point: 3,
                      date: '2024-01-14',
                      teacher_name: 'Math Teacher',
                      note: '95% on algebra test',
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    };
  } else {
    // Student BPS data
    return {
      success: true,
      records: [
        {
          id: 1,
          item_title: 'Excellent Class Performance',
          item_type: 'PRS',
          item_point: 3,
          date: '2024-01-15',
          note: 'Outstanding participation and problem-solving in advanced mathematics',
          teacher_name: 'Sarah Johnson',
          status: 1,
          subject: 'Mathematics',
        },
        {
          id: 2,
          item_title: 'Helping Classmates',
          item_type: 'PRS',
          item_point: 2,
          date: '2024-01-12',
          note: 'Voluntarily helped struggling students during physics lab session',
          teacher_name: 'Dr. Wilson',
          status: 1,
          subject: 'Physics',
        },
        {
          id: 3,
          item_title: 'Creative Project Presentation',
          item_type: 'PRS',
          item_point: 3,
          date: '2024-01-11',
          note: 'Innovative approach to computer science project with excellent presentation skills',
          teacher_name: 'Mr. Tech',
          status: 1,
          subject: 'Computer Science',
        },
        {
          id: 4,
          item_title: 'Perfect Attendance Week',
          item_type: 'PRS',
          item_point: 2,
          date: '2024-01-08',
          note: 'Perfect attendance and punctuality for the entire week',
          teacher_name: 'Homeroom Teacher',
          status: 1,
          subject: 'General',
        },
        {
          id: 5,
          item_title: 'Leadership in Group Work',
          item_type: 'PRS',
          item_point: 2,
          date: '2024-01-05',
          note: 'Excellent leadership and collaboration in biology lab group project',
          teacher_name: 'Dr. Green',
          status: 1,
          subject: 'Biology',
        },
        {
          id: 6,
          item_title: 'Late Assignment Submission',
          item_type: 'DPS',
          item_point: -1,
          date: '2024-01-10',
          note: 'History essay submitted 1 day after deadline without prior notice',
          teacher_name: 'Ms. Brown',
          status: 1,
          subject: 'History',
        },
        {
          id: 7,
          item_title: 'Improved Behavior',
          item_type: 'PRS',
          item_point: 1,
          date: '2024-01-14',
          note: 'Significant improvement in class focus and participation',
          teacher_name: 'Prof. White',
          status: 1,
          subject: 'Chemistry',
        },
        {
          id: 8,
          item_title: 'Outstanding Art Project',
          item_type: 'PRS',
          item_point: 3,
          date: '2024-01-09',
          note: 'Exceptional creativity and technique in mixed media artwork',
          teacher_name: 'Mr. Clark',
          status: 1,
          subject: 'Art',
        },
        {
          id: 9,
          item_title: 'Volunteer Service',
          item_type: 'PRS',
          item_point: 2,
          date: '2024-01-06',
          note: 'Volunteered to help organize school library during free period',
          teacher_name: 'Librarian',
          status: 1,
          subject: 'Community Service',
        },
        {
          id: 10,
          item_title: 'Forgot Materials',
          item_type: 'DPS',
          item_point: -1,
          date: '2024-01-07',
          note: 'Came to PE class without proper sports attire',
          teacher_name: 'Coach Davis',
          status: 1,
          subject: 'Physical Education',
        },
      ],
      total_points: 17,
      positive_points: 18,
      negative_points: -2,
      monthly_summary: {
        january: {
          positive: 18,
          negative: -2,
          total: 16,
        },
        december: {
          positive: 12,
          negative: -1,
          total: 11,
        },
      },
      rank_in_class: 3,
      class_average: 12.5,
    };
  }
};

// Demo attendance data
export const getDemoAttendanceData = () => {
  return {
    success: true,
    attendance_records: [
      {
        date: '2024-01-15',
        status: 'present',
        period: 'Full Day',
        remarks: '',
        check_in_time: '07:55',
        check_out_time: '15:30',
      },
      {
        date: '2024-01-14',
        status: 'present',
        period: 'Full Day',
        remarks: '',
        check_in_time: '07:58',
        check_out_time: '15:30',
      },
      {
        date: '2024-01-13',
        status: 'late',
        period: 'Full Day',
        remarks: 'Arrived 15 minutes late due to traffic',
        check_in_time: '08:15',
        check_out_time: '15:30',
      },
      {
        date: '2024-01-12',
        status: 'absent',
        period: 'Full Day',
        remarks: 'Sick leave - flu symptoms',
        check_in_time: null,
        check_out_time: null,
      },
      {
        date: '2024-01-11',
        status: 'present',
        period: 'Full Day',
        remarks: '',
        check_in_time: '07:52',
        check_out_time: '15:30',
      },
      {
        date: '2024-01-10',
        status: 'present',
        period: 'Full Day',
        remarks: '',
        check_in_time: '07:56',
        check_out_time: '15:30',
      },
      {
        date: '2024-01-09',
        status: 'present',
        period: 'Full Day',
        remarks: '',
        check_in_time: '07:54',
        check_out_time: '15:30',
      },
      {
        date: '2024-01-08',
        status: 'present',
        period: 'Full Day',
        remarks: '',
        check_in_time: '07:59',
        check_out_time: '15:30',
      },
      {
        date: '2024-01-07',
        status: 'late',
        period: 'Full Day',
        remarks: 'Medical appointment - arrived after first period',
        check_in_time: '09:20',
        check_out_time: '15:30',
      },
      {
        date: '2024-01-06',
        status: 'present',
        period: 'Full Day',
        remarks: '',
        check_in_time: '07:53',
        check_out_time: '15:30',
      },
      {
        date: '2024-01-05',
        status: 'present',
        period: 'Full Day',
        remarks: '',
        check_in_time: '07:57',
        check_out_time: '15:30',
      },
      {
        date: '2024-01-04',
        status: 'absent',
        period: 'Full Day',
        remarks: 'Family emergency',
        check_in_time: null,
        check_out_time: null,
      },
      {
        date: '2024-01-03',
        status: 'present',
        period: 'Full Day',
        remarks: '',
        check_in_time: '07:55',
        check_out_time: '15:30',
      },
      {
        date: '2024-01-02',
        status: 'present',
        period: 'Full Day',
        remarks: '',
        check_in_time: '07:58',
        check_out_time: '15:30',
      },
      {
        date: '2024-01-01',
        status: 'holiday',
        period: 'Full Day',
        remarks: 'New Year Holiday',
        check_in_time: null,
        check_out_time: null,
      },
    ],
    daily_statistics: [
      { date: '2024-01-15', present: 1, absent: 0, late: 0, excused: 0 },
      { date: '2024-01-14', present: 1, absent: 0, late: 0, excused: 0 },
      { date: '2024-01-13', present: 0, absent: 0, late: 1, excused: 0 },
      { date: '2024-01-12', present: 0, absent: 1, late: 0, excused: 0 },
      { date: '2024-01-11', present: 1, absent: 0, late: 0, excused: 0 },
    ],
    summary_statistics: {
      total_days: 20,
      present_days: 17,
      absent_days: 2,
      late_days: 1,
      excused_days: 0,
      attendance_percentage: 85.0,
    },
  };
};

// Demo grades data
export const getDemoGradesData = () => {
  return {
    success: true,
    subjects: [
      {
        subject_id: 1,
        subject_name: 'Mathematics',
        teacher: 'Sarah Johnson',
        subject_code: 'MATH10A',
        credit_hours: 4,
        assessments: [
          {
            assessment_id: 1,
            assessment_name: 'Mid-term Examination',
            assessment_type: 'Exam',
            date: '2024-01-10',
            score: 87,
            max_score: 100,
            percentage: 87.0,
            grade: 'A',
            weight: 30,
            feedback:
              'Excellent problem-solving skills, minor calculation errors',
          },
          {
            assessment_id: 2,
            assessment_name: 'Algebra Quiz',
            assessment_type: 'Quiz',
            date: '2024-01-05',
            score: 19,
            max_score: 20,
            percentage: 95.0,
            grade: 'A+',
            weight: 15,
            feedback: 'Perfect understanding of algebraic concepts',
          },
          {
            assessment_id: 3,
            assessment_name: 'Geometry Assignment',
            assessment_type: 'Assignment',
            date: '2024-01-12',
            score: 23,
            max_score: 25,
            percentage: 92.0,
            grade: 'A+',
            weight: 20,
            feedback: 'Creative approach to geometric proofs',
          },
          {
            assessment_id: 4,
            assessment_name: 'Class Participation',
            assessment_type: 'Participation',
            date: '2024-01-15',
            score: 85,
            max_score: 100,
            percentage: 85.0,
            grade: 'A',
            weight: 10,
            feedback: 'Active participation, good questions',
          },
        ],
        overall_grade: 'A',
        overall_percentage: 89.2,
        letter_grade: 'A',
        gpa_points: 4.0,
      },
      {
        subject_id: 2,
        subject_name: 'Physics',
        teacher: 'Dr. Wilson',
        subject_code: 'PHY10A',
        credit_hours: 4,
        assessments: [
          {
            assessment_id: 5,
            assessment_name: 'Mechanics Test',
            assessment_type: 'Exam',
            date: '2024-01-08',
            score: 82,
            max_score: 100,
            percentage: 82.0,
            grade: 'B+',
            weight: 25,
            feedback: 'Good grasp of concepts, work on problem setup',
          },
          {
            assessment_id: 6,
            assessment_name: 'Lab Report - Pendulum',
            assessment_type: 'Lab Report',
            date: '2024-01-12',
            score: 24,
            max_score: 25,
            percentage: 96.0,
            grade: 'A+',
            weight: 20,
            feedback: 'Excellent data analysis and conclusions',
          },
          {
            assessment_id: 7,
            assessment_name: 'Forces Quiz',
            assessment_type: 'Quiz',
            date: '2024-01-14',
            score: 17,
            max_score: 20,
            percentage: 85.0,
            grade: 'A',
            weight: 15,
            feedback: 'Strong understanding of force vectors',
          },
        ],
        overall_grade: 'A-',
        overall_percentage: 86.8,
        letter_grade: 'A-',
        gpa_points: 3.7,
      },
      {
        subject_id: 3,
        subject_name: 'English Literature',
        teacher: 'John Smith',
        subject_code: 'ENG10A',
        credit_hours: 3,
        assessments: [
          {
            assessment_id: 8,
            assessment_name: 'Shakespeare Essay',
            assessment_type: 'Essay',
            date: '2024-01-09',
            score: 88,
            max_score: 100,
            percentage: 88.0,
            grade: 'A',
            weight: 25,
            feedback: 'Insightful analysis, excellent use of textual evidence',
          },
          {
            assessment_id: 9,
            assessment_name: 'Poetry Analysis',
            assessment_type: 'Assignment',
            date: '2024-01-13',
            score: 92,
            max_score: 100,
            percentage: 92.0,
            grade: 'A+',
            weight: 20,
            feedback: 'Creative interpretation with strong literary devices',
          },
        ],
        overall_grade: 'A',
        overall_percentage: 89.5,
        letter_grade: 'A',
        gpa_points: 4.0,
      },
      {
        subject_id: 4,
        subject_name: 'Chemistry',
        teacher: 'Prof. White',
        subject_code: 'CHEM10A',
        credit_hours: 4,
        assessments: [
          {
            assessment_id: 10,
            assessment_name: 'Atomic Structure Test',
            assessment_type: 'Exam',
            date: '2024-01-11',
            score: 78,
            max_score: 100,
            percentage: 78.0,
            grade: 'B+',
            weight: 30,
            feedback: 'Good understanding, review electron configurations',
          },
          {
            assessment_id: 11,
            assessment_name: 'Lab Safety Quiz',
            assessment_type: 'Quiz',
            date: '2024-01-06',
            score: 20,
            max_score: 20,
            percentage: 100.0,
            grade: 'A+',
            weight: 10,
            feedback: 'Perfect score on safety procedures',
          },
        ],
        overall_grade: 'B+',
        overall_percentage: 82.4,
        letter_grade: 'B+',
        gpa_points: 3.3,
      },
      {
        subject_id: 5,
        subject_name: 'World History',
        teacher: 'Ms. Brown',
        subject_code: 'HIST10A',
        credit_hours: 3,
        assessments: [
          {
            assessment_id: 12,
            assessment_name: 'Ancient Civilizations Test',
            assessment_type: 'Exam',
            date: '2024-01-07',
            score: 85,
            max_score: 100,
            percentage: 85.0,
            grade: 'A',
            weight: 25,
            feedback: 'Strong knowledge of historical facts and timelines',
          },
          {
            assessment_id: 13,
            assessment_name: 'Research Project',
            assessment_type: 'Project',
            date: '2024-01-14',
            score: 90,
            max_score: 100,
            percentage: 90.0,
            grade: 'A+',
            weight: 30,
            feedback: 'Excellent research and presentation skills',
          },
        ],
        overall_grade: 'A',
        overall_percentage: 87.8,
        letter_grade: 'A',
        gpa_points: 4.0,
      },
    ],
    overall_gpa: 3.8,
    weighted_gpa: 3.9,
    class_rank: 3,
    total_students: 28,
    semester: 'Spring 2024',
    academic_year: '2023-2024',
    grade_level: '10th Grade',
  };
};

// Demo homework data
export const getDemoHomeworkData = () => {
  return {
    success: true,
    assignments: [
      {
        homework_id: 1,
        title: 'Quadratic Equations Problem Set',
        subject: 'Mathematics',
        teacher: 'Sarah Johnson',
        subject_code: 'MATH10A',
        description:
          'Complete exercises 1-25 from Chapter 5: Quadratic Equations. Show all work and include graphs for problems 20-25.',
        due_date: '2024-01-20',
        assigned_date: '2024-01-15',
        status: 'pending',
        priority: 'high',
        estimated_time: '2.5 hours',
        attachments: ['Chapter5_Exercises.pdf', 'Graphing_Template.pdf'],
        instructions:
          'Use graphing calculator for verification. Submit handwritten solutions.',
      },
      {
        homework_id: 2,
        title: 'Pendulum Motion Lab Report',
        subject: 'Physics',
        teacher: 'Dr. Wilson',
        subject_code: 'PHY10A',
        description:
          'Write a comprehensive lab report on the pendulum experiment conducted in class. Include data analysis, error calculations, and conclusions.',
        due_date: '2024-01-18',
        assigned_date: '2024-01-12',
        status: 'completed',
        priority: 'medium',
        estimated_time: '3 hours',
        submitted_date: '2024-01-17',
        grade: 'A',
        score: 24,
        max_score: 25,
        feedback:
          'Excellent data analysis and clear presentation. Minor formatting issues in bibliography.',
        attachments: ['Lab_Report_Template.docx', 'Data_Sheet.xlsx'],
      },
      {
        homework_id: 3,
        title: 'World War II Impact Essay',
        subject: 'World History',
        teacher: 'Ms. Brown',
        subject_code: 'HIST10A',
        description:
          'Write a 1000-word analytical essay on the social and economic impact of World War II on civilian populations.',
        due_date: '2024-01-25',
        assigned_date: '2024-01-10',
        status: 'in_progress',
        priority: 'high',
        estimated_time: '4 hours',
        progress: 65,
        attachments: ['Essay_Guidelines.pdf', 'Primary_Sources.pdf'],
        instructions: 'Use at least 5 primary sources. Follow MLA format.',
      },
      {
        homework_id: 4,
        title: 'Chemical Bonding Worksheet',
        subject: 'Chemistry',
        teacher: 'Prof. White',
        subject_code: 'CHEM10A',
        description:
          'Complete the chemical bonding worksheet focusing on ionic and covalent bonds.',
        due_date: '2024-01-19',
        assigned_date: '2024-01-14',
        status: 'completed',
        priority: 'medium',
        estimated_time: '1.5 hours',
        submitted_date: '2024-01-18',
        grade: 'B+',
        score: 18,
        max_score: 20,
        feedback:
          'Good understanding of concepts. Review Lewis structures for complex molecules.',
      },
      {
        homework_id: 5,
        title: 'Shakespeare Sonnet Analysis',
        subject: 'English Literature',
        teacher: 'John Smith',
        subject_code: 'ENG10A',
        description:
          'Analyze Sonnet 18 by William Shakespeare. Focus on literary devices, themes, and historical context.',
        due_date: '2024-01-22',
        assigned_date: '2024-01-16',
        status: 'pending',
        priority: 'medium',
        estimated_time: '2 hours',
        attachments: ['Sonnet_Analysis_Guide.pdf'],
        instructions: 'Minimum 500 words. Include textual evidence.',
      },
      {
        homework_id: 6,
        title: 'Photosynthesis Lab Preparation',
        subject: 'Biology',
        teacher: 'Dr. Green',
        subject_code: 'BIO10A',
        description:
          "Read Chapter 8 and answer pre-lab questions 1-15. Prepare hypothesis for next week's experiment.",
        due_date: '2024-01-21',
        assigned_date: '2024-01-15',
        status: 'completed',
        priority: 'low',
        estimated_time: '1 hour',
        submitted_date: '2024-01-20',
        grade: 'A+',
        score: 15,
        max_score: 15,
        feedback: 'Excellent preparation and thoughtful hypothesis.',
      },
      {
        homework_id: 7,
        title: 'French Vocabulary Quiz Prep',
        subject: 'French Language',
        teacher: 'Mme. Dubois',
        subject_code: 'FR10A',
        description:
          'Study vocabulary from Unit 3: Family and Relationships. Quiz covers 50 words and phrases.',
        due_date: '2024-01-23',
        assigned_date: '2024-01-16',
        status: 'pending',
        priority: 'medium',
        estimated_time: '1.5 hours',
        attachments: ['Unit3_Vocabulary.pdf', 'Audio_Pronunciation.mp3'],
        instructions: 'Practice pronunciation using audio files.',
      },
      {
        homework_id: 8,
        title: 'Computer Programming Project',
        subject: 'Computer Science',
        teacher: 'Mr. Tech',
        subject_code: 'CS10A',
        description:
          'Create a simple calculator program using Python. Include basic arithmetic operations and error handling.',
        due_date: '2024-01-28',
        assigned_date: '2024-01-14',
        status: 'in_progress',
        priority: 'high',
        estimated_time: '5 hours',
        progress: 40,
        attachments: ['Project_Requirements.pdf', 'Starter_Code.py'],
        instructions: 'Submit both source code and documentation.',
      },
      {
        homework_id: 9,
        title: 'Art Portfolio Piece',
        subject: 'Art & Design',
        teacher: 'Mr. Clark',
        subject_code: 'ART10A',
        description:
          'Create a mixed media artwork exploring the theme of "Identity". Use at least 3 different materials.',
        due_date: '2024-01-30',
        assigned_date: '2024-01-10',
        status: 'in_progress',
        priority: 'medium',
        estimated_time: '6 hours',
        progress: 75,
        instructions: 'Document your creative process with photos.',
      },
      {
        homework_id: 10,
        title: 'Geography Map Project',
        subject: 'Geography',
        teacher: 'Ms. Taylor',
        subject_code: 'GEO10A',
        description:
          'Create a detailed map of climate zones in South America. Include legend, scale, and analysis.',
        due_date: '2024-01-17',
        assigned_date: '2024-01-08',
        status: 'overdue',
        priority: 'high',
        estimated_time: '3 hours',
        days_overdue: 2,
        instructions: 'Use colored pencils or digital tools.',
      },
    ],
    statistics: {
      total_assignments: 25,
      completed: 18,
      pending: 5,
      in_progress: 3,
      overdue: 1,
      completion_rate: 72.0,
      average_grade: 'A-',
      total_study_time: 45.5,
    },
    upcoming_deadlines: [
      {
        homework_id: 10,
        title: 'Geography Map Project',
        due_date: '2024-01-17',
        status: 'overdue',
        days_overdue: 2,
      },
      {
        homework_id: 4,
        title: 'Chemical Bonding Worksheet',
        due_date: '2024-01-19',
        status: 'completed',
      },
      {
        homework_id: 1,
        title: 'Quadratic Equations Problem Set',
        due_date: '2024-01-20',
        status: 'pending',
      },
    ],
  };
};

// Demo notification data
export const getDemoNotificationData = () => {
  return {
    success: true,
    notifications: [
      {
        notification_id: 1,
        notification_uid: 'demo_notif_1',
        title: 'New Assignment Posted',
        body: 'Mathematics: Quadratic Equations Problem Set due January 20th',
        type: 'homework',
        is_read: false,
        created_at: '2024-01-15T10:30:00Z',
        priority: 'high',
        data: {
          homework_id: 1,
          subject: 'Mathematics',
          teacher: 'Sarah Johnson',
          due_date: '2024-01-20',
        },
      },
      {
        notification_id: 2,
        notification_uid: 'demo_notif_2',
        title: 'Grade Posted',
        body: 'Physics Lab Report: Grade A (24/25) - Excellent work!',
        type: 'grades',
        is_read: false,
        created_at: '2024-01-15T09:15:00Z',
        priority: 'medium',
        data: {
          assessment_id: 6,
          subject: 'Physics',
          grade: 'A',
          score: 24,
          max_score: 25,
        },
      },
      {
        notification_id: 3,
        notification_uid: 'demo_notif_3',
        title: 'Positive Behavior Recognition',
        body: 'You received 3 points for excellent class performance in Mathematics',
        type: 'behavior',
        is_read: false,
        created_at: '2024-01-15T08:45:00Z',
        priority: 'medium',
        data: {
          bps_id: 1,
          points: 3,
          item_type: 'PRS',
          subject: 'Mathematics',
        },
      },
      {
        notification_id: 4,
        notification_uid: 'demo_notif_4',
        title: 'Assignment Overdue',
        body: 'Geography Map Project is 2 days overdue. Please submit as soon as possible.',
        type: 'homework',
        is_read: true,
        created_at: '2024-01-14T16:00:00Z',
        priority: 'urgent',
        data: {
          homework_id: 10,
          subject: 'Geography',
          days_overdue: 2,
        },
      },
      {
        notification_id: 5,
        notification_uid: 'demo_notif_5',
        title: 'Attendance Alert',
        body: 'You were marked late today. Please ensure punctual arrival.',
        type: 'attendance',
        is_read: true,
        created_at: '2024-01-14T08:15:00Z',
        priority: 'medium',
        data: {
          date: '2024-01-14',
          status: 'late',
          arrival_time: '08:15',
        },
      },
      {
        notification_id: 6,
        notification_uid: 'demo_notif_6',
        title: 'Upcoming Quiz',
        body: 'French Vocabulary Quiz scheduled for January 23rd. Study Unit 3 materials.',
        type: 'announcement',
        is_read: true,
        created_at: '2024-01-13T14:30:00Z',
        priority: 'medium',
        data: {
          subject: 'French Language',
          quiz_date: '2024-01-23',
          topic: 'Unit 3: Family and Relationships',
        },
      },
      {
        notification_id: 7,
        notification_uid: 'demo_notif_7',
        title: 'Parent-Teacher Conference',
        body: 'Parent-Teacher Conference scheduled for January 25th at 3:00 PM',
        type: 'announcement',
        is_read: true,
        created_at: '2024-01-12T11:00:00Z',
        priority: 'high',
        data: {
          conference_date: '2024-01-25',
          conference_time: '15:00',
          location: 'Main Office',
        },
      },
      {
        notification_id: 8,
        notification_uid: 'demo_notif_8',
        title: 'Library Book Due',
        body: 'Return "To Kill a Mockingbird" by January 22nd to avoid late fees',
        type: 'library',
        is_read: true,
        created_at: '2024-01-12T09:30:00Z',
        priority: 'low',
        data: {
          book_title: 'To Kill a Mockingbird',
          due_date: '2024-01-22',
          isbn: '978-0-06-112008-4',
        },
      },
      {
        notification_id: 9,
        notification_uid: 'demo_notif_9',
        title: 'Science Fair Registration',
        body: 'Science Fair registration deadline is January 30th. Submit your project proposal.',
        type: 'announcement',
        is_read: true,
        created_at: '2024-01-11T13:45:00Z',
        priority: 'medium',
        data: {
          event: 'Science Fair',
          deadline: '2024-01-30',
          requirement: 'Project Proposal',
        },
      },
      {
        notification_id: 10,
        notification_uid: 'demo_notif_10',
        title: 'Perfect Attendance Week',
        body: 'Congratulations! You achieved perfect attendance this week.',
        type: 'behavior',
        is_read: true,
        created_at: '2024-01-11T15:30:00Z',
        priority: 'low',
        data: {
          bps_id: 4,
          points: 2,
          item_type: 'PRS',
          achievement: 'Perfect Attendance Week',
        },
      },
      {
        notification_id: 11,
        notification_uid: 'demo_notif_11',
        title: 'Cafeteria Menu Update',
        body: 'New vegetarian options available starting Monday. Check the updated menu.',
        type: 'announcement',
        is_read: true,
        created_at: '2024-01-10T12:00:00Z',
        priority: 'low',
        data: {
          category: 'Cafeteria',
          effective_date: '2024-01-15',
        },
      },
      {
        notification_id: 12,
        notification_uid: 'demo_notif_12',
        title: 'Field Trip Permission',
        body: 'Science Museum field trip on February 5th. Permission slip required by January 28th.',
        type: 'announcement',
        is_read: true,
        created_at: '2024-01-09T10:15:00Z',
        priority: 'high',
        data: {
          event: 'Science Museum Field Trip',
          trip_date: '2024-02-05',
          permission_deadline: '2024-01-28',
        },
      },
    ],
    unread_count: 3,
    total_count: 12,
    categories: [
      {
        type: 'homework',
        label: 'Homework',
        count: 2,
        unread_count: 1,
      },
      {
        type: 'grades',
        label: 'Grades',
        count: 1,
        unread_count: 1,
      },
      {
        type: 'behavior',
        label: 'Behavior',
        count: 2,
        unread_count: 1,
      },
      {
        type: 'attendance',
        label: 'Attendance',
        count: 1,
        unread_count: 0,
      },
      {
        type: 'announcement',
        label: 'Announcements',
        count: 5,
        unread_count: 0,
      },
      {
        type: 'library',
        label: 'Library',
        count: 1,
        unread_count: 0,
      },
    ],
  };
};

// Demo library data
export const getDemoLibraryData = () => {
  return {
    success: true,
    borrowed_books: [
      {
        book_id: 1,
        title: 'To Kill a Mockingbird',
        author: 'Harper Lee',
        isbn: '978-0-06-112008-4',
        borrowed_date: '2024-01-08',
        due_date: '2024-01-22',
        status: 'borrowed',
        days_remaining: 7,
        renewal_count: 0,
        max_renewals: 2,
      },
      {
        book_id: 2,
        title: 'The Great Gatsby',
        author: 'F. Scott Fitzgerald',
        isbn: '978-0-7432-7356-5',
        borrowed_date: '2024-01-05',
        due_date: '2024-01-19',
        status: 'overdue',
        days_overdue: 1,
        fine_amount: 0.5,
        renewal_count: 1,
        max_renewals: 2,
      },
      {
        book_id: 3,
        title: 'Introduction to Physics',
        author: 'John D. Cutnell',
        isbn: '978-1-118-48689-4',
        borrowed_date: '2024-01-10',
        due_date: '2024-01-24',
        status: 'borrowed',
        days_remaining: 9,
        renewal_count: 0,
        max_renewals: 3,
      },
    ],
    statistics: {
      total_borrowed: 15,
      currently_borrowed: 3,
      overdue_items: 1,
      total_fines: 0.5,
      books_read_this_year: 12,
      favorite_genre: 'Science Fiction',
    },
    borrowing_limits: {
      max_books: 5,
      current_books: 3,
      available_slots: 2,
      loan_period_days: 14,
    },
    recent_returns: [
      {
        title: '1984',
        author: 'George Orwell',
        returned_date: '2024-01-12',
        rating: 5,
      },
      {
        title: 'Pride and Prejudice',
        author: 'Jane Austen',
        returned_date: '2024-01-10',
        rating: 4,
      },
    ],
  };
};

// Demo messaging data
export const getDemoMessagingData = () => {
  return {
    success: true,
    conversations: [
      {
        conversation_id: 1,
        conversation_uuid: 'demo-conv-1',
        topic: 'Mathematics Homework Help',
        creator: {
          id: 1,
          name: 'Sarah Johnson',
          user_type: 'staff',
        },
        members: [
          { id: 1, name: 'Sarah Johnson', user_type: 'staff', photo: null },
          { id: 2, name: 'Alex Chen', user_type: 'student', photo: null },
          { id: 3, name: 'Emma Wilson', user_type: 'student', photo: null },
        ],
        last_message: {
          content:
            'Great work on the quadratic equations! Let me know if you need more help.',
          sender_id: 1,
          created_at: '2024-01-15T14:30:00Z',
          message_type: 'text',
        },
        unread_count: 0,
        created_at: '2024-01-14T10:00:00Z',
        updated_at: '2024-01-15T14:30:00Z',
      },
      {
        conversation_id: 2,
        conversation_uuid: 'demo-conv-2',
        topic: 'Science Project Group',
        creator: {
          id: 2,
          name: 'Alex Chen',
          user_type: 'student',
        },
        members: [
          { id: 2, name: 'Alex Chen', user_type: 'student', photo: null },
          { id: 3, name: 'Emma Wilson', user_type: 'student', photo: null },
          {
            id: 4,
            name: 'Michael Rodriguez',
            user_type: 'student',
            photo: null,
          },
        ],
        last_message: {
          content: 'Meeting tomorrow at 3 PM in the library for project work.',
          sender_id: 2,
          created_at: '2024-01-15T16:45:00Z',
          message_type: 'text',
        },
        unread_count: 2,
        created_at: '2024-01-13T09:15:00Z',
        updated_at: '2024-01-15T16:45:00Z',
      },
    ],
    messages: {
      'demo-conv-1': [
        {
          message_id: 1,
          content: 'I need help with problem 15 from the homework set.',
          sender: {
            id: 2,
            name: 'Alex Chen',
            user_type: 'student',
          },
          created_at: '2024-01-15T13:00:00Z',
          message_type: 'text',
          is_own_message: true,
        },
        {
          message_id: 2,
          content:
            'Of course! Problem 15 is about completing the square. Let me walk you through it step by step.',
          sender: {
            id: 1,
            name: 'Sarah Johnson',
            user_type: 'staff',
          },
          created_at: '2024-01-15T13:15:00Z',
          message_type: 'text',
          is_own_message: false,
        },
        {
          message_id: 3,
          content:
            'Thank you! That explanation really helped me understand the concept.',
          sender: {
            id: 2,
            name: 'Alex Chen',
            user_type: 'student',
          },
          created_at: '2024-01-15T14:00:00Z',
          message_type: 'text',
          is_own_message: true,
        },
      ],
    },
  };
};

// Demo teacher classes data (for comprehensive student data)
export const getDemoTeacherClassesData = () => {
  return {
    success: true,
    data: {
      branches: [
        {
          branch_id: 1,
          branch_name: 'Main Campus',
          classes: [
            {
              class_id: 1,
              class_name: 'Grade 10A',
              subject_id: 1,
              subject_name: 'Mathematics',
              student_count: 28,
              students: [
                {
                  student_id: 1,
                  student_name: 'Alex Chen',
                  student_code: 'STU2024001',
                  grade: '10',
                  photo: null,
                },
                {
                  student_id: 2,
                  student_name: 'Emma Wilson',
                  student_code: 'STU2024002',
                  grade: '10',
                  photo: null,
                },
                {
                  student_id: 3,
                  student_name: 'Michael Rodriguez',
                  student_code: 'STU2024003',
                  grade: '10',
                  photo: null,
                },
                {
                  student_id: 4,
                  student_name: 'Sophia Kim',
                  student_code: 'STU2024004',
                  grade: '10',
                  photo: null,
                },
                {
                  student_id: 5,
                  student_name: 'James Thompson',
                  student_code: 'STU2024005',
                  grade: '10',
                  photo: null,
                },
                // Add more students to reach 28 total
                ...Array.from({ length: 23 }, (_, i) => ({
                  student_id: 6 + i,
                  student_name: `Student ${6 + i}`,
                  student_code: `STU2024${String(6 + i).padStart(3, '0')}`,
                  grade: '10',
                  photo: null,
                })),
              ],
            },
            {
              class_id: 2,
              class_name: 'Grade 11B',
              subject_id: 2,
              subject_name: 'Physics',
              student_count: 24,
              students: [
                {
                  student_id: 29,
                  student_name: 'Isabella Garcia',
                  student_code: 'STU2024029',
                  grade: '11',
                  photo: null,
                },
                {
                  student_id: 30,
                  student_name: 'David Park',
                  student_code: 'STU2024030',
                  grade: '11',
                  photo: null,
                },
                // Add more students to reach 24 total
                ...Array.from({ length: 22 }, (_, i) => ({
                  student_id: 31 + i,
                  student_name: `Student ${31 + i}`,
                  student_code: `STU2024${String(31 + i).padStart(3, '0')}`,
                  grade: '11',
                  photo: null,
                })),
              ],
            },
            {
              class_id: 3,
              class_name: 'Grade 9A',
              subject_id: 1,
              subject_name: 'Mathematics',
              student_count: 26,
              students: [
                // Add 26 students for Grade 9A
                ...Array.from({ length: 26 }, (_, i) => ({
                  student_id: 53 + i,
                  student_name: `Grade 9 Student ${i + 1}`,
                  student_code: `STU2024${String(53 + i).padStart(3, '0')}`,
                  grade: '9',
                  photo: null,
                })),
              ],
            },
          ],
        },
        {
          branch_id: 2,
          branch_name: 'Secondary Campus',
          classes: [
            {
              class_id: 4,
              class_name: 'Grade 9C',
              subject_id: 1,
              subject_name: 'Mathematics',
              student_count: 23,
              students: [
                {
                  student_id: 79,
                  student_name: 'Olivia Johnson',
                  student_code: 'STU2024079',
                  grade: '9',
                  photo: null,
                },
                // Add more students to reach 23 total
                ...Array.from({ length: 22 }, (_, i) => ({
                  student_id: 80 + i,
                  student_name: `Secondary Student ${i + 1}`,
                  student_code: `STU2024${String(80 + i).padStart(3, '0')}`,
                  grade: '9',
                  photo: null,
                })),
              ],
            },
            {
              class_id: 5,
              class_name: 'Grade 7A',
              subject_id: 1,
              subject_name: 'Mathematics',
              student_count: 27,
              students: [
                // Add 27 students for Grade 7A
                ...Array.from({ length: 27 }, (_, i) => ({
                  student_id: 102 + i,
                  student_name: `Grade 7 Student ${i + 1}`,
                  student_code: `STU2024${String(102 + i).padStart(3, '0')}`,
                  grade: '7',
                  photo: null,
                })),
              ],
            },
          ],
        },
        {
          branch_id: 3,
          branch_name: 'International Campus',
          classes: [
            {
              class_id: 6,
              class_name: 'IB Math HL',
              subject_id: 1,
              subject_name: 'Mathematics',
              student_count: 18,
              students: [
                // Add 18 students for IB Math HL
                ...Array.from({ length: 18 }, (_, i) => ({
                  student_id: 129 + i,
                  student_name: `IB Student ${i + 1}`,
                  student_code: `STU2024${String(129 + i).padStart(3, '0')}`,
                  grade: '12',
                  photo: null,
                })),
              ],
            },
            {
              class_id: 7,
              class_name: 'IB Math SL',
              subject_id: 1,
              subject_name: 'Mathematics',
              student_count: 20,
              students: [
                // Add 20 students for IB Math SL
                ...Array.from({ length: 20 }, (_, i) => ({
                  student_id: 147 + i,
                  student_name: `IB SL Student ${i + 1}`,
                  student_code: `STU2024${String(147 + i).padStart(3, '0')}`,
                  grade: '11',
                  photo: null,
                })),
              ],
            },
          ],
        },
      ],
    },
  };
};

// Demo teacher homework data (for teacher homework management)
export const getDemoTeacherHomeworkData = () => {
  return {
    success: true,
    data: [
      {
        homework_id: 1,
        title: 'Quadratic Equations Problem Set',
        subject: 'Mathematics',
        class_name: 'Grade 10A',
        description:
          'Complete exercises 1-25 from Chapter 5: Quadratic Equations. Show all work and include graphs for problems 20-25.',
        due_date: '2024-01-20',
        assigned_date: '2024-01-15',
        status: 'active',
        submission_count: 18,
        total_students: 28,
        completion_rate: 64.3,
      },
      {
        homework_id: 2,
        title: 'Physics Lab Report - Pendulum Motion',
        subject: 'Physics',
        class_name: 'Grade 11B',
        description:
          'Write a comprehensive lab report on the pendulum experiment conducted in class. Include data analysis, error calculations, and conclusions.',
        due_date: '2024-01-18',
        assigned_date: '2024-01-12',
        status: 'closed',
        submission_count: 24,
        total_students: 24,
        completion_rate: 100.0,
      },
      {
        homework_id: 3,
        title: 'Algebra Review Worksheet',
        subject: 'Mathematics',
        class_name: 'Grade 9A',
        description:
          'Review worksheet covering linear equations, inequalities, and basic graphing concepts.',
        due_date: '2024-01-22',
        assigned_date: '2024-01-16',
        status: 'active',
        submission_count: 12,
        total_students: 26,
        completion_rate: 46.2,
      },
      {
        homework_id: 4,
        title: 'Advanced Calculus Problems',
        subject: 'Mathematics',
        class_name: 'Grade 12A',
        description:
          'Solve integration and differentiation problems from Chapter 8. Focus on applications in real-world scenarios.',
        due_date: '2024-01-25',
        assigned_date: '2024-01-17',
        status: 'active',
        submission_count: 8,
        total_students: 22,
        completion_rate: 36.4,
      },
      {
        homework_id: 5,
        title: 'Basic Mathematics Fundamentals',
        subject: 'Mathematics',
        class_name: 'Grade 8B',
        description:
          'Practice problems on fractions, decimals, and percentages. Complete all exercises in workbook pages 45-52.',
        due_date: '2024-01-21',
        assigned_date: '2024-01-14',
        status: 'active',
        submission_count: 25,
        total_students: 30,
        completion_rate: 83.3,
      },
    ],
  };
};

// Demo teacher attendance data (for attendance taking screen)
export const getDemoTeacherAttendanceData = (timetableId) => {
  // Map timetable IDs to specific classes with students
  const classStudentMap = {
    1: {
      // Grade 10A Mathematics
      gradeName: 'Grade 10A',
      subjectName: 'Mathematics',
      students: [
        {
          student_id: 1,
          student_name: 'Alex Chen',
          student_photo: null,
          roll_number: '10A001',
          classroom_name: 'Grade 10A',
          attendance_status: 'present',
        },
        {
          student_id: 2,
          student_name: 'Emma Wilson',
          student_photo: null,
          roll_number: '10A002',
          classroom_name: 'Grade 10A',
          attendance_status: 'present',
        },
        {
          student_id: 3,
          student_name: 'Michael Rodriguez',
          student_photo: null,
          roll_number: '10A003',
          classroom_name: 'Grade 10A',
          attendance_status: 'late',
        },
        {
          student_id: 4,
          student_name: 'Sophia Kim',
          student_photo: null,
          roll_number: '10A004',
          classroom_name: 'Grade 10A',
          attendance_status: 'present',
        },
        {
          student_id: 5,
          student_name: 'James Thompson',
          student_photo: null,
          roll_number: '10A005',
          classroom_name: 'Grade 10A',
          attendance_status: 'absent',
        },
        {
          student_id: 6,
          student_name: 'Olivia Martinez',
          student_photo: null,
          roll_number: '10A006',
          classroom_name: 'Grade 10A',
          attendance_status: 'present',
        },
        {
          student_id: 7,
          student_name: 'William Davis',
          student_photo: null,
          roll_number: '10A007',
          classroom_name: 'Grade 10A',
          attendance_status: 'present',
        },
        {
          student_id: 8,
          student_name: 'Ava Johnson',
          student_photo: null,
          roll_number: '10A008',
          classroom_name: 'Grade 10A',
          attendance_status: 'present',
        },
        {
          student_id: 9,
          student_name: 'Benjamin Brown',
          student_photo: null,
          roll_number: '10A009',
          classroom_name: 'Grade 10A',
          attendance_status: 'present',
        },
        {
          student_id: 10,
          student_name: 'Charlotte Garcia',
          student_photo: null,
          roll_number: '10A010',
          classroom_name: 'Grade 10A',
          attendance_status: 'present',
        },
        // Add more students to reach 28 total
        ...Array.from({ length: 18 }, (_, i) => ({
          student_id: 11 + i,
          student_name: `Student ${11 + i}`,
          student_photo: null,
          roll_number: `10A${String(11 + i).padStart(3, '0')}`,
          classroom_name: 'Grade 10A',
          attendance_status: 'present',
        })),
      ],
    },
    2: {
      // Grade 11B Physics
      gradeName: 'Grade 11B',
      subjectName: 'Physics',
      students: [
        {
          student_id: 29,
          student_name: 'Isabella Garcia',
          student_photo: null,
          roll_number: '11B001',
          classroom_name: 'Grade 11B',
          attendance_status: 'present',
        },
        {
          student_id: 30,
          student_name: 'David Park',
          student_photo: null,
          roll_number: '11B002',
          classroom_name: 'Grade 11B',
          attendance_status: 'present',
        },
        {
          student_id: 31,
          student_name: 'Luna Zhang',
          student_photo: null,
          roll_number: '11B003',
          classroom_name: 'Grade 11B',
          attendance_status: 'late',
        },
        {
          student_id: 32,
          student_name: 'Ethan Kumar',
          student_photo: null,
          roll_number: '11B004',
          classroom_name: 'Grade 11B',
          attendance_status: 'present',
        },
        {
          student_id: 33,
          student_name: 'Maya Patel',
          student_photo: null,
          roll_number: '11B005',
          classroom_name: 'Grade 11B',
          attendance_status: 'present',
        },
        // Add more students to reach 24 total
        ...Array.from({ length: 19 }, (_, i) => ({
          student_id: 34 + i,
          student_name: `Grade 11B Student ${i + 6}`,
          student_photo: null,
          roll_number: `11B${String(6 + i).padStart(3, '0')}`,
          classroom_name: 'Grade 11B',
          attendance_status: 'present',
        })),
      ],
    },
    3: {
      // Grade 9A Mathematics
      gradeName: 'Grade 9A',
      subjectName: 'Mathematics',
      students: [
        {
          student_id: 53,
          student_name: 'Noah Anderson',
          student_photo: null,
          roll_number: '9A001',
          classroom_name: 'Grade 9A',
          attendance_status: 'present',
        },
        {
          student_id: 54,
          student_name: 'Zoe Williams',
          student_photo: null,
          roll_number: '9A002',
          classroom_name: 'Grade 9A',
          attendance_status: 'present',
        },
        {
          student_id: 55,
          student_name: 'Lucas Miller',
          student_photo: null,
          roll_number: '9A003',
          classroom_name: 'Grade 9A',
          attendance_status: 'absent',
        },
        {
          student_id: 56,
          student_name: 'Aria Taylor',
          student_photo: null,
          roll_number: '9A004',
          classroom_name: 'Grade 9A',
          attendance_status: 'present',
        },
        {
          student_id: 57,
          student_name: 'Mason Lee',
          student_photo: null,
          roll_number: '9A005',
          classroom_name: 'Grade 9A',
          attendance_status: 'present',
        },
        // Add more students to reach 26 total
        ...Array.from({ length: 21 }, (_, i) => ({
          student_id: 58 + i,
          student_name: `Grade 9A Student ${i + 6}`,
          student_photo: null,
          roll_number: `9A${String(6 + i).padStart(3, '0')}`,
          classroom_name: 'Grade 9A',
          attendance_status: 'present',
        })),
      ],
    },
  };

  // Default class for unknown timetable IDs
  const defaultClass = {
    gradeName: 'Demo Class',
    subjectName: 'Demo Subject',
    students: [
      {
        student_id: 999,
        student_name: 'Demo Student',
        student_photo: null,
        roll_number: 'DEMO001',
        classroom_name: 'Demo Class',
        attendance_status: 'present',
      },
    ],
  };

  const classData = classStudentMap[timetableId] || defaultClass;

  return {
    success: true,
    students: classData.students,
    class_info: {
      grade_name: classData.gradeName,
      subject_name: classData.subjectName,
      total_students: classData.students.length,
    },
  };
};

// Demo student attendance data
export const getDemoStudentAttendanceData = () => {
  return {
    success: true,
    attendance_records: [
      {
        date: '2024-01-15',
        weekday: 'Monday',
        subject: 'Mathematics',
        period: '1',
        status: 'PRESENT',
        attendance_note: null,
      },
      {
        date: '2024-01-15',
        weekday: 'Monday',
        subject: 'English Literature',
        period: '2',
        status: 'PRESENT',
        attendance_note: null,
      },
      {
        date: '2024-01-15',
        weekday: 'Monday',
        subject: 'Physics',
        period: '3',
        status: 'LATE',
        attendance_note: 'Arrived 10 minutes late',
      },
      {
        date: '2024-01-15',
        weekday: 'Monday',
        subject: 'World History',
        period: '4',
        status: 'PRESENT',
        attendance_note: null,
      },
      {
        date: '2024-01-15',
        weekday: 'Monday',
        subject: 'Physical Education',
        period: '5',
        status: 'PRESENT',
        attendance_note: null,
      },
      {
        date: '2024-01-14',
        weekday: 'Sunday',
        subject: 'Mathematics',
        period: '1',
        status: 'PRESENT',
        attendance_note: null,
      },
      {
        date: '2024-01-14',
        weekday: 'Sunday',
        subject: 'Chemistry',
        period: '2',
        status: 'PRESENT',
        attendance_note: null,
      },
      {
        date: '2024-01-14',
        weekday: 'Sunday',
        subject: 'English Literature',
        period: '3',
        status: 'ABSENT',
        attendance_note: 'Sick leave',
      },
      {
        date: '2024-01-14',
        weekday: 'Sunday',
        subject: 'Biology',
        period: '4',
        status: 'ABSENT',
        attendance_note: 'Sick leave',
      },
      {
        date: '2024-01-13',
        weekday: 'Saturday',
        subject: 'Physics',
        period: '1',
        status: 'PRESENT',
        attendance_note: null,
      },
      {
        date: '2024-01-13',
        weekday: 'Saturday',
        subject: 'Mathematics',
        period: '2',
        status: 'PRESENT',
        attendance_note: null,
      },
      {
        date: '2024-01-13',
        weekday: 'Saturday',
        subject: 'Computer Science',
        period: '3',
        status: 'PRESENT',
        attendance_note: null,
      },
      {
        date: '2024-01-12',
        weekday: 'Friday',
        subject: 'World History',
        period: '1',
        status: 'PRESENT',
        attendance_note: null,
      },
      {
        date: '2024-01-12',
        weekday: 'Friday',
        subject: 'Art',
        period: '2',
        status: 'LATE',
        attendance_note: 'Traffic delay',
      },
      {
        date: '2024-01-12',
        weekday: 'Friday',
        subject: 'Physical Education',
        period: '3',
        status: 'PRESENT',
        attendance_note: null,
      },
      {
        date: '2024-01-11',
        weekday: 'Thursday',
        subject: 'English Literature',
        period: '1',
        status: 'PRESENT',
        attendance_note: null,
      },
      {
        date: '2024-01-11',
        weekday: 'Thursday',
        subject: 'Chemistry',
        period: '2',
        status: 'PRESENT',
        attendance_note: null,
      },
      {
        date: '2024-01-11',
        weekday: 'Thursday',
        subject: 'Mathematics',
        period: '3',
        status: 'PRESENT',
        attendance_note: null,
      },
      {
        date: '2024-01-10',
        weekday: 'Wednesday',
        subject: 'Biology',
        period: '1',
        status: 'PRESENT',
        attendance_note: null,
      },
      {
        date: '2024-01-10',
        weekday: 'Wednesday',
        subject: 'Physics',
        period: '2',
        status: 'PRESENT',
        attendance_note: null,
      },
    ],
    daily_statistics: [
      {
        date: '2024-01-15',
        weekday: 'Monday',
        present_count: 4,
        absent_count: 0,
        late_count: 1,
        total_periods: 5,
        attendance_percentage: 80.0,
      },
      {
        date: '2024-01-14',
        weekday: 'Sunday',
        present_count: 2,
        absent_count: 2,
        late_count: 0,
        total_periods: 4,
        attendance_percentage: 50.0,
      },
      {
        date: '2024-01-13',
        weekday: 'Saturday',
        present_count: 3,
        absent_count: 0,
        late_count: 0,
        total_periods: 3,
        attendance_percentage: 100.0,
      },
      {
        date: '2024-01-12',
        weekday: 'Friday',
        present_count: 2,
        absent_count: 0,
        late_count: 1,
        total_periods: 3,
        attendance_percentage: 66.7,
      },
      {
        date: '2024-01-11',
        weekday: 'Thursday',
        present_count: 3,
        absent_count: 0,
        late_count: 0,
        total_periods: 3,
        attendance_percentage: 100.0,
      },
      {
        date: '2024-01-10',
        weekday: 'Wednesday',
        present_count: 2,
        absent_count: 0,
        late_count: 0,
        total_periods: 2,
        attendance_percentage: 100.0,
      },
    ],
    summary_statistics: {
      total_periods: 20,
      present_count: 16,
      absent_count: 2,
      late_count: 2,
      overall_attendance_percentage: 80.0,
      monthly_attendance_percentage: 80.0,
      weekly_attendance_percentage: 85.0,
    },
  };
};

// Demo student grades data
export const getDemoStudentGradesData = () => {
  return {
    success: true,
    summative: [
      {
        id: 1,
        subject_name: 'Mathematics',
        assessment_name: 'Algebra Test 1',
        grade: 'A',
        percentage: 92,
        max_marks: 100,
        obtained_marks: 92,
        date: '2024-01-15',
        teacher: 'Sarah Johnson',
        category: 'Test',
        weight: 20,
      },
      {
        id: 2,
        subject_name: 'Mathematics',
        assessment_name: 'Geometry Quiz',
        grade: 'B+',
        percentage: 87,
        max_marks: 50,
        obtained_marks: 43.5,
        date: '2024-01-12',
        teacher: 'Sarah Johnson',
        category: 'Quiz',
        weight: 10,
      },
      {
        id: 3,
        subject_name: 'Physics',
        assessment_name: 'Motion and Forces Test',
        grade: 'A-',
        percentage: 89,
        max_marks: 100,
        obtained_marks: 89,
        date: '2024-01-14',
        teacher: 'Dr. Wilson',
        category: 'Test',
        weight: 25,
      },
      {
        id: 4,
        subject_name: 'Physics',
        assessment_name: 'Lab Report - Pendulum',
        grade: 'A',
        percentage: 95,
        max_marks: 50,
        obtained_marks: 47.5,
        date: '2024-01-10',
        teacher: 'Dr. Wilson',
        category: 'Lab Report',
        weight: 15,
      },
      {
        id: 5,
        subject_name: 'English Literature',
        assessment_name: 'Essay - Shakespeare Analysis',
        grade: 'B+',
        percentage: 85,
        max_marks: 100,
        obtained_marks: 85,
        date: '2024-01-13',
        teacher: 'John Smith',
        category: 'Essay',
        weight: 30,
      },
      {
        id: 6,
        subject_name: 'English Literature',
        assessment_name: 'Poetry Interpretation',
        grade: 'A-',
        percentage: 88,
        max_marks: 75,
        obtained_marks: 66,
        date: '2024-01-08',
        teacher: 'John Smith',
        category: 'Assignment',
        weight: 20,
      },
      {
        id: 7,
        subject_name: 'World History',
        assessment_name: 'Ancient Civilizations Test',
        grade: 'B',
        percentage: 82,
        max_marks: 100,
        obtained_marks: 82,
        date: '2024-01-11',
        teacher: 'Ms. Brown',
        category: 'Test',
        weight: 25,
      },
      {
        id: 8,
        subject_name: 'World History',
        assessment_name: 'Research Project - Roman Empire',
        grade: 'A',
        percentage: 94,
        max_marks: 100,
        obtained_marks: 94,
        date: '2024-01-09',
        teacher: 'Ms. Brown',
        category: 'Project',
        weight: 35,
      },
      {
        id: 9,
        subject_name: 'Chemistry',
        assessment_name: 'Periodic Table Quiz',
        grade: 'A-',
        percentage: 90,
        max_marks: 50,
        obtained_marks: 45,
        date: '2024-01-07',
        teacher: 'Prof. White',
        category: 'Quiz',
        weight: 15,
      },
      {
        id: 10,
        subject_name: 'Chemistry',
        assessment_name: 'Chemical Reactions Lab',
        grade: 'B+',
        percentage: 86,
        max_marks: 75,
        obtained_marks: 64.5,
        date: '2024-01-05',
        teacher: 'Prof. White',
        category: 'Lab Report',
        weight: 20,
      },
    ],
    formative: [
      {
        id: 11,
        subject_name: 'Mathematics',
        assessment_name: 'Daily Practice - Equations',
        grade: 'Excellent',
        percentage: 95,
        max_marks: 20,
        obtained_marks: 19,
        date: '2024-01-15',
        teacher: 'Sarah Johnson',
        category: 'Practice',
        feedback: 'Great understanding of algebraic concepts',
      },
      {
        id: 12,
        subject_name: 'Physics',
        assessment_name: 'Class Participation',
        grade: 'Good',
        percentage: 85,
        max_marks: 10,
        obtained_marks: 8.5,
        date: '2024-01-14',
        teacher: 'Dr. Wilson',
        category: 'Participation',
        feedback: 'Active in discussions, good questions',
      },
      {
        id: 13,
        subject_name: 'English Literature',
        assessment_name: 'Reading Comprehension',
        grade: 'Very Good',
        percentage: 88,
        max_marks: 25,
        obtained_marks: 22,
        date: '2024-01-13',
        teacher: 'John Smith',
        category: 'Exercise',
        feedback: 'Shows good analytical skills',
      },
      {
        id: 14,
        subject_name: 'World History',
        assessment_name: 'Timeline Activity',
        grade: 'Excellent',
        percentage: 92,
        max_marks: 15,
        obtained_marks: 13.8,
        date: '2024-01-12',
        teacher: 'Ms. Brown',
        category: 'Activity',
        feedback: 'Accurate chronological understanding',
      },
      {
        id: 15,
        subject_name: 'Chemistry',
        assessment_name: 'Homework - Balancing Equations',
        grade: 'Good',
        percentage: 83,
        max_marks: 30,
        obtained_marks: 24.9,
        date: '2024-01-11',
        teacher: 'Prof. White',
        category: 'Homework',
        feedback: 'Minor errors in complex equations',
      },
    ],
    subject_averages: {
      Mathematics: { average: 89.5, grade: 'A-' },
      Physics: { average: 92.0, grade: 'A' },
      'English Literature': { average: 86.5, grade: 'B+' },
      'World History': { average: 88.0, grade: 'B+' },
      Chemistry: { average: 88.0, grade: 'B+' },
    },
    overall_gpa: 3.7,
    overall_percentage: 88.8,
  };
};

// Demo student homework/assignments data
export const getDemoStudentHomeworkData = () => {
  return [
    {
      id: 1,
      title: 'Algebra Problem Set Chapter 5',
      subject: 'Mathematics',
      description:
        'Complete exercises 1-25 from Chapter 5: Quadratic Equations. Show all work and include graphs for problems 20-25.',
      due_date: '2024-01-20',
      assigned_date: '2024-01-15',
      status: 'pending',
      priority: 'high',
      estimated_time: '2 hours',
      teacher: 'Sarah Johnson',
      attachments: [],
      is_completed: false,
    },
    {
      id: 2,
      title: 'Physics Lab Report - Pendulum Motion',
      subject: 'Physics',
      description:
        'Write a comprehensive lab report on the pendulum experiment conducted in class. Include data analysis, error calculations, and conclusions.',
      due_date: '2024-01-18',
      assigned_date: '2024-01-12',
      status: 'completed',
      priority: 'medium',
      estimated_time: '3 hours',
      teacher: 'Dr. Wilson',
      attachments: ['lab_data.xlsx'],
      is_completed: true,
      completed_date: '2024-01-17',
    },
    {
      id: 3,
      title: 'Essay - Shakespeare Analysis',
      subject: 'English Literature',
      description:
        'Write a 1000-word essay analyzing the themes of power and corruption in Macbeth. Use at least 3 scholarly sources.',
      due_date: '2024-01-22',
      assigned_date: '2024-01-10',
      status: 'in_progress',
      priority: 'high',
      estimated_time: '4 hours',
      teacher: 'John Smith',
      attachments: ['essay_guidelines.pdf'],
      is_completed: false,
    },
    {
      id: 4,
      title: 'World History Timeline Project',
      subject: 'World History',
      description:
        'Create a detailed timeline of major events during the Roman Empire (27 BC - 476 AD). Include at least 20 significant events.',
      due_date: '2024-01-25',
      assigned_date: '2024-01-14',
      status: 'pending',
      priority: 'medium',
      estimated_time: '3 hours',
      teacher: 'Ms. Brown',
      attachments: ['timeline_template.docx'],
      is_completed: false,
    },
    {
      id: 5,
      title: 'Chemistry Homework - Balancing Equations',
      subject: 'Chemistry',
      description:
        'Balance the chemical equations on pages 45-47 of the textbook. Show your work for each equation.',
      due_date: '2024-01-19',
      assigned_date: '2024-01-16',
      status: 'pending',
      priority: 'low',
      estimated_time: '1 hour',
      teacher: 'Prof. White',
      attachments: [],
      is_completed: false,
    },
    {
      id: 6,
      title: 'Biology Cell Structure Diagram',
      subject: 'Biology',
      description:
        'Draw and label a detailed diagram of a plant cell and an animal cell. Include all major organelles and their functions.',
      due_date: '2024-01-21',
      assigned_date: '2024-01-13',
      status: 'completed',
      priority: 'medium',
      estimated_time: '2 hours',
      teacher: 'Dr. Green',
      attachments: [],
      is_completed: true,
      completed_date: '2024-01-20',
    },
    {
      id: 7,
      title: 'Computer Science - Python Programming',
      subject: 'Computer Science',
      description:
        'Write a Python program that calculates the factorial of a number using both iterative and recursive methods.',
      due_date: '2024-01-23',
      assigned_date: '2024-01-15',
      status: 'in_progress',
      priority: 'high',
      estimated_time: '2 hours',
      teacher: 'Mr. Tech',
      attachments: ['starter_code.py'],
      is_completed: false,
    },
    {
      id: 8,
      title: 'Art Project - Still Life Drawing',
      subject: 'Art',
      description:
        'Create a detailed still life drawing using charcoal. Focus on light, shadow, and texture.',
      due_date: '2024-01-26',
      assigned_date: '2024-01-11',
      status: 'pending',
      priority: 'low',
      estimated_time: '3 hours',
      teacher: 'Ms. Artist',
      attachments: [],
      is_completed: false,
    },
  ];
};

// Demo student BPS/behavior data
export const getDemoStudentBPSData = () => {
  return {
    success: true,
    behavior_records: [
      {
        id: 1,
        item_title: 'Excellent Homework Submission',
        item_type: 'PRS',
        item_point: 3,
        date: '2024-01-15',
        teacher_name: 'Sarah Johnson',
        note: 'Outstanding work on algebra problems',
        subject: 'Mathematics',
      },
      {
        id: 2,
        item_title: 'Active Class Participation',
        item_type: 'PRS',
        item_point: 2,
        date: '2024-01-14',
        teacher_name: 'Dr. Wilson',
        note: 'Excellent questions and engagement in physics class',
        subject: 'Physics',
      },
      {
        id: 3,
        item_title: 'Helping Classmates',
        item_type: 'PRS',
        item_point: 2,
        date: '2024-01-12',
        teacher_name: 'John Smith',
        note: 'Assisted struggling students during English literature discussion',
        subject: 'English Literature',
      },
      {
        id: 4,
        item_title: 'Late to Class',
        item_type: 'DPS',
        item_point: -1,
        date: '2024-01-11',
        teacher_name: 'Ms. Brown',
        note: 'Arrived 10 minutes late to World History class',
        subject: 'World History',
      },
      {
        id: 5,
        item_title: 'Outstanding Science Fair Project',
        item_type: 'PRS',
        item_point: 5,
        date: '2024-01-10',
        teacher_name: 'Dr. Green',
        note: 'Exceptional research on renewable energy sources',
        subject: 'Biology',
      },
      {
        id: 6,
        item_title: 'Leadership in Group Project',
        item_type: 'PRS',
        item_point: 3,
        date: '2024-01-09',
        teacher_name: 'Mr. Tech',
        note: 'Excellent leadership in computer science project',
        subject: 'Computer Science',
      },
      {
        id: 7,
        item_title: 'Forgot Homework',
        item_type: 'DPS',
        item_point: -2,
        date: '2024-01-08',
        teacher_name: 'Prof. White',
        note: 'Did not submit chemistry homework on time',
        subject: 'Chemistry',
      },
      {
        id: 8,
        item_title: 'Perfect Attendance Week',
        item_type: 'PRS',
        item_point: 2,
        date: '2024-01-05',
        teacher_name: 'Homeroom Teacher',
        note: 'Perfect attendance for the entire week',
        subject: 'General',
      },
    ],
    detention_records: [
      {
        id: 1,
        reason: 'Disrupting Class',
        date: '2024-01-08',
        time: '15:30',
        duration: '30 minutes',
        teacher: 'Prof. White',
        status: 'completed',
        notes: 'Student was talking during chemistry lecture',
      },
    ],
    summary: {
      total_prs_points: 17,
      total_dps_points: -3,
      net_points: 14,
      current_level: 'Good Standing',
      recent_trend: 'Improving',
    },
  };
};

// Demo student library data
export const getDemoStudentLibraryData = () => {
  return {
    success: true,
    borrowed_books: [
      {
        id: 1,
        title: 'Advanced Mathematics: Calculus and Beyond',
        author: 'Dr. Sarah Mitchell',
        isbn: '978-0-123456-78-9',
        barcode: 'LIB001234567',
        borrowed_date: '2024-01-10',
        due_date: '2024-01-24',
        days_remaining: 9,
        status: 'borrowed',
        category: 'Mathematics',
        location: 'Science Section - Shelf A3',
        cover_image: null,
      },
      {
        id: 2,
        title: 'Physics: Principles and Applications',
        author: 'Prof. Michael Johnson, Dr. Jane Davis',
        isbn: '978-0-987654-32-1',
        barcode: 'LIB002345678',
        borrowed_date: '2024-01-08',
        due_date: '2024-01-22',
        days_remaining: 7,
        status: 'borrowed',
        category: 'Physics',
        location: 'Science Section - Shelf B2',
        cover_image: null,
      },
      {
        id: 3,
        title: 'World Literature: Classic and Contemporary',
        author: 'Emma Thompson, John Smith, Sarah Wilson',
        isbn: '978-0-456789-01-2',
        barcode: 'LIB003456789',
        borrowed_date: '2024-01-12',
        due_date: '2024-01-26',
        days_remaining: 11,
        status: 'borrowed',
        category: 'Literature',
        location: 'Humanities Section - Shelf C1',
        cover_image: null,
      },
    ],
    overdue_books: [
      {
        id: 4,
        title: 'Chemistry Lab Manual',
        author: 'Dr. Robert White',
        isbn: '978-0-234567-89-0',
        barcode: 'LIB004567890',
        borrowed_date: '2023-12-20',
        due_date: '2024-01-03',
        days_overdue: 12,
        status: 'overdue',
        category: 'Chemistry',
        location: 'Science Section - Shelf D1',
        cover_image: null,
        fine_amount: 6.0,
      },
    ],
    reserved_books: [
      {
        id: 5,
        title: 'Computer Science Algorithms',
        author: 'Dr. Lisa Chen',
        isbn: '978-0-345678-90-1',
        barcode: 'LIB005678901',
        reserved_date: '2024-01-14',
        expected_available: '2024-01-20',
        status: 'reserved',
        category: 'Computer Science',
        location: 'Technology Section - Shelf E2',
        cover_image: null,
        queue_position: 2,
      },
    ],
    reading_history: [
      {
        id: 6,
        title: 'Biology: Cell Structure and Function',
        author: 'Dr. Maria Garcia',
        isbn: '978-0-567890-12-3',
        barcode: 'LIB006789012',
        borrowed_date: '2023-12-01',
        returned_date: '2023-12-15',
        status: 'returned',
        category: 'Biology',
        rating: 5,
      },
      {
        id: 7,
        title: 'History of Ancient Civilizations',
        author: 'Prof. David Brown',
        isbn: '978-0-678901-23-4',
        barcode: 'LIB007890123',
        borrowed_date: '2023-11-15',
        returned_date: '2023-11-29',
        status: 'returned',
        category: 'History',
        rating: 4,
      },
    ],
    library_statistics: {
      total_books_borrowed: 15,
      books_currently_borrowed: 3,
      overdue_books: 1,
      reserved_books: 1,
      total_fines: 6.0,
      reading_streak_days: 45,
      favorite_category: 'Science',
      books_read_this_month: 2,
      books_read_this_year: 8,
    },
    borrowing_limits: {
      max_books: 5,
      current_borrowed: 3,
      available_slots: 2,
      max_reservation: 3,
      current_reserved: 1,
      max_renewal: 2,
    },
    library_info: {
      name: 'Central School Library',
      hours: 'Mon-Fri: 8:00 AM - 6:00 PM, Sat: 9:00 AM - 4:00 PM',
      contact: '<EMAIL>',
      phone: '+****************',
      location: 'Main Building, 2nd Floor',
    },
  };
};

// Demo personal calendar data
export const getDemoPersonalCalendarData = (userType = 'student') => {
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);
  const nextWeek = new Date(today);
  nextWeek.setDate(today.getDate() + 7);
  const nextMonth = new Date(today);
  nextMonth.setMonth(today.getMonth() + 1);

  const formatDate = (date) => date.toISOString().split('T')[0];

  if (userType === 'student') {
    return {
      success: true,
      user_id: 123,
      user_type: 'student',
      personal_events: [
        {
          id: 'homework_123',
          title: 'Math Assignment - Due',
          description: 'Complete exercises 1-10\nSubject: Mathematics',
          start_date: formatDate(tomorrow),
          end_date: formatDate(tomorrow),
          start_time: null,
          end_time: null,
          is_all_day: true,
          location: '',
          status: 'pending',
          source: 'homework_due',
          category: 'homework',
          priority: 'high',
          metadata: {
            homework_id: 123,
            subject_name: 'Mathematics',
            is_completed: false,
          },
        },
        {
          id: 'exam_789',
          title: 'Science Test',
          description: 'Chapter 5-7 Test\nSubject: Biology\nType: Summative',
          start_date: formatDate(nextWeek),
          end_date: formatDate(nextWeek),
          start_time: '09:00:00',
          end_time: '11:00:00',
          is_all_day: false,
          location: 'Room 201',
          status: 'scheduled',
          source: 'exam_schedule',
          category: 'exam',
          priority: 'high',
          metadata: {
            exam_id: 789,
            subject_name: 'Biology',
            exam_type: 'Summative',
          },
        },
        {
          id: 'homework_456',
          title: 'English Essay - Due',
          description:
            'Write a 500-word essay on Shakespeare\nSubject: English Literature',
          start_date: formatDate(nextWeek),
          end_date: formatDate(nextWeek),
          start_time: null,
          end_time: null,
          is_all_day: true,
          location: '',
          status: 'pending',
          source: 'homework_due',
          category: 'homework',
          priority: 'medium',
          metadata: {
            homework_id: 456,
            subject_name: 'English Literature',
            is_completed: false,
          },
        },
      ],
      total_events: 3,
      date_range: {
        start_date: formatDate(today),
        end_date: formatDate(nextMonth),
      },
      generated_at: new Date().toISOString(),
    };
  } else if (userType === 'teacher') {
    return {
      success: true,
      user_id: 456,
      user_type: 'teacher',
      personal_events: [
        {
          id: 'homework_review_123',
          title: 'Math Assignment - Review Due',
          description:
            'Review homework submissions\nSubject: Mathematics\nSubmissions: 18/25',
          start_date: formatDate(tomorrow),
          end_date: formatDate(tomorrow),
          start_time: null,
          end_time: null,
          is_all_day: true,
          location: '',
          status: 'pending_review',
          source: 'homework_review',
          category: 'homework',
          priority: 'medium',
          metadata: {
            homework_id: 123,
            subject_name: 'Mathematics',
            submission_count: 18,
            total_students: 25,
          },
        },
        {
          id: 'exam_conduct_789',
          title: 'Science Test - Conduct',
          description:
            'Conduct Biology test\nSubject: Biology\nClass: Grade 10A',
          start_date: formatDate(nextWeek),
          end_date: formatDate(nextWeek),
          start_time: '09:00:00',
          end_time: '11:00:00',
          is_all_day: false,
          location: 'Room 201',
          status: 'scheduled',
          source: 'exam_conduct',
          category: 'exam',
          priority: 'high',
          metadata: {
            exam_id: 789,
            subject_name: 'Biology',
            class_name: 'Grade 10A',
            student_count: 25,
          },
        },
        {
          id: 'birthday_321',
          title: "John Smith's Birthday",
          description:
            'Student birthday\nClass: Grade 10A\nTurning: 16 years old',
          start_date: formatDate(nextWeek),
          end_date: formatDate(nextWeek),
          start_time: null,
          end_time: null,
          is_all_day: true,
          location: '',
          status: 'scheduled',
          source: 'student_birthday',
          category: 'birthday',
          priority: 'low',
          metadata: {
            student_id: 321,
            student_name: 'John Smith',
            class_name: 'Grade 10A',
            age_turning: 16,
          },
        },
      ],
      total_events: 3,
      date_range: {
        start_date: formatDate(today),
        end_date: formatDate(nextMonth),
      },
      generated_at: new Date().toISOString(),
    };
  }

  return {
    success: false,
    message: 'Invalid user type',
  };
};

// Demo health data
export const getDemoStudentHealthRecords = () => ({
  success: true,
  data: {
    student: {
      id: 123,
      name: 'Demo Student',
      branch_id: 1,
    },
    records: [
      {
        record_id: 456,
        date: '2024-01-15',
        time: '10:30',
        reason: 'Headache,Fever',
        action: 'Rest,Medication',
        parent_contact_time: '11:00',
        temperature: '38.5°C',
        medication: 'Paracetamol',
        comments: 'Student feeling better after rest',
        time_left_nurse_clinic: '12:00',
        created_by: 'Nurse Smith',
        created_at: '2024-01-15T10:30:00.000000Z',
        updated_at: '2024-01-15T12:00:00.000000Z',
      },
      {
        record_id: 457,
        date: '2024-01-10',
        time: '14:15',
        reason: 'Stomach ache',
        action: 'Rest',
        parent_contact_time: '14:30',
        temperature: '37.2°C',
        medication: 'Antacid',
        comments: 'Ate too quickly at lunch',
        time_left_nurse_clinic: '15:00',
        created_by: 'Nurse Johnson',
        created_at: '2024-01-10T14:15:00.000000Z',
        updated_at: '2024-01-10T15:00:00.000000Z',
      },
      {
        record_id: 458,
        date: '2024-01-05',
        time: '09:45',
        reason: 'Minor cut',
        action: 'First aid,Bandage',
        parent_contact_time: null,
        temperature: null,
        medication: 'Antiseptic',
        comments: 'Small cut on finger during art class',
        time_left_nurse_clinic: '10:00',
        created_by: 'Nurse Smith',
        created_at: '2024-01-05T09:45:00.000000Z',
        updated_at: '2024-01-05T10:00:00.000000Z',
      },
    ],
    total_count: 3,
  },
});

export const getDemoStudentHealthInfo = () => ({
  success: true,
  data: {
    student: {
      id: 123,
      name: 'Demo Student',
    },
    health_info: {
      student_id: 123,
      medical_conditions: 'Mild asthma',
      regularly_used_medication: 'Inhaler (Salbutamol)',
      has_vision_problem: null,
      vision_check_date: '2023-09-01',
      hearing_issue: null,
      special_food_consideration: 'No nuts, No shellfish',
      allergies: 'Peanuts, Shellfish, Pollen',
      allergy_symtoms: 'Swelling, difficulty breathing, skin rash',
      allergy_first_aid: 'Use EpiPen immediately, call emergency services',
      allowed_drugs: 'Paracetamol,Ibuprofen,Antihistamine',
      emergency_name_1: 'Jane Doe',
      emergency_name_2: 'Bob Doe',
      emergency_phone_1: '+**********',
      emergency_phone_2: '+**********',
    },
    measurements: {
      latest_measurement: {
        id: 220,
        height: '139.00',
        weight: '46.00',
        date: '2025-07-16',
        created_at: '2025-07-25T09:53:56.000000Z',
      },
      measurement_history: [
        {
          id: 220,
          height: '139.00',
          weight: '46.00',
          date: '2025-07-16',
          created_at: '2025-07-25T09:53:56.000000Z',
        },
        {
          id: 219,
          height: '137.00',
          weight: '44.50',
          date: '2025-01-15',
          created_at: '2025-01-20T10:30:00.000000Z',
        },
      ],
      total_measurements: 2,
      has_measurements: true,
    },
  },
});

export const getDemoTeacherHealthData = () => ({
  success: true,
  data: {
    access_level: 'nurse',
    teacher: {
      id: 789,
      name: 'Demo Teacher',
      branch_id: 1,
      access_description:
        'Full access to all health records (nurse permissions)',
    },
    student_records: [
      {
        record_id: 456,
        student_id: 123,
        student_name: 'Demo Student',
        date: '2024-01-15',
        time: '10:30',
        reason: 'Headache,Fever',
        action: 'Rest,Medication',
        parent_contact_time: '11:00',
        temperature: '38.5°C',
        medication: 'Paracetamol',
        comments: 'Student feeling better after rest',
        created_by: 'Nurse Smith',
      },
      {
        record_id: 459,
        student_id: 124,
        student_name: 'Sarah Johnson',
        date: '2024-01-14',
        time: '13:20',
        reason: 'Nausea',
        action: 'Rest,Hydration',
        parent_contact_time: '13:45',
        temperature: '37.0°C',
        medication: null,
        comments: 'Feeling better after rest',
        created_by: 'Nurse Smith',
      },
    ],
    staff_records: [
      {
        record_id: 789,
        user_id: 456,
        staff_name: 'Demo Staff',
        date: '2024-01-14',
        time: '14:00',
        reason: 'Back pain',
        action: 'Rest recommended',
        temperature: null,
        medication: 'Pain relief',
        comments: 'Work-related strain',
      },
    ],
    guest_records: [
      {
        record_id: 101,
        guest_name: 'Demo Visitor',
        date: '2024-01-13',
        time: '09:15',
        reason: 'Minor cut',
        action: 'First aid applied',
        temperature: null,
        medication: 'Antiseptic',
        comments: 'Small cut on finger',
      },
    ],
    students: [
      {
        id: 123,
        name: 'Demo Student',
        email: '<EMAIL>',
      },
      {
        id: 124,
        name: 'Sarah Johnson',
        email: '<EMAIL>',
      },
    ],
    staff: [
      {
        id: 456,
        name: 'Demo Staff',
        email: '<EMAIL>',
      },
    ],
    statistics: {
      total_student_records: 2,
      total_staff_records: 1,
      total_guest_records: 1,
      records_today: 0,
      records_this_week: 4,
    },
  },
});

export const getDemoHealthLookupData = () => ({
  success: true,
  data: {
    injuries: [
      {
        id: 1,
        value: 'Headache',
        description: 'Pain in head or neck area',
      },
      {
        id: 2,
        value: 'Fever',
        description: 'Elevated body temperature',
      },
      {
        id: 3,
        value: 'Stomach ache',
        description: 'Abdominal pain or discomfort',
      },
      {
        id: 4,
        value: 'Nausea',
        description: 'Feeling of sickness',
      },
      {
        id: 5,
        value: 'Dizziness',
        description: 'Feeling lightheaded or unsteady',
      },
      {
        id: 6,
        value: 'Minor cut',
        description: 'Small wound or laceration',
      },
      {
        id: 7,
        value: 'Bruise',
        description: 'Injury causing discoloration',
      },
      {
        id: 8,
        value: 'Sprain',
        description: 'Injury to ligament',
      },
    ],
    actions: [
      {
        id: 1,
        value: 'Rest',
        description: 'Allow patient to rest',
      },
      {
        id: 2,
        value: 'Medication',
        description: 'Administer appropriate medication',
      },
      {
        id: 3,
        value: 'First aid',
        description: 'Apply basic first aid treatment',
      },
      {
        id: 4,
        value: 'Ice pack',
        description: 'Apply cold compress',
      },
      {
        id: 5,
        value: 'Bandage',
        description: 'Apply protective bandaging',
      },
      {
        id: 6,
        value: 'Hydration',
        description: 'Provide fluids',
      },
    ],
    medications: [
      {
        id: 1,
        value: 'Paracetamol',
        description: 'Pain and fever relief',
      },
      {
        id: 2,
        value: 'Ibuprofen',
        description: 'Anti-inflammatory pain relief',
      },
      {
        id: 3,
        value: 'Antiseptic',
        description: 'Wound cleaning and disinfection',
      },
      {
        id: 4,
        value: 'Antacid',
        description: 'Stomach acid relief',
      },
      {
        id: 5,
        value: 'Antihistamine',
        description: 'Allergy symptom relief',
      },
    ],
  },
});

// Export demo credentials for easy access
export { getDemoCredentials };
