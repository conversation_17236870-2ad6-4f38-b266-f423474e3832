name: Build all platforms

on:
  workflow_dispatch:
    inputs:
      profile:
        description: 'Build profile'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - preview
          - development

jobs:
  build_android:
    name: Build Android
    type: build
    params:
      platform: android
      profile: ${{ inputs.profile || 'production' }}
      auto_submit: false

  build_ios:
    name: Build iOS
    type: build
    params:
      platform: ios
      profile: ${{ inputs.profile || 'production' }}
      auto_submit: false

  notify_completion:
    name: Notify All Builds Completion
    type: function
    needs: [build_android, build_ios]
    params:
      script: |
        echo "All platform builds completed successfully!"
        echo "Android Build ID: ${{ needs.build_android.outputs.build_id }}"
        echo "Android Build URL: ${{ needs.build_android.outputs.build_url }}"
        echo "iOS Build ID: ${{ needs.build_ios.outputs.build_id }}"
        echo "iOS Build URL: ${{ needs.build_ios.outputs.build_url }}"
