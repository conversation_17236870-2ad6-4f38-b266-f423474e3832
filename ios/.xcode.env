# This `.xcode.env` file is versioned and is used to source the environment
# used when running script phases inside Xcode.
# To customize your local environment, you can create an `.xcode.env.local`
# file that is not versioned.

# NODE_BINARY variable contains the PATH to the node executable.
#
# Customize the NODE_BINARY variable here.
# For example, to use nvm with brew, add the following line
# . "$(brew --prefix nvm)/nvm.sh" --no-use

# Try to find Node.js in common locations for Xcode Cloud compatibility
if command -v node >/dev/null 2>&1; then
    export NODE_BINARY=$(command -v node)
elif [ -x "/usr/local/bin/node" ]; then
    export NODE_BINARY="/usr/local/bin/node"
elif [ -x "/opt/homebrew/bin/node" ]; then
    export NODE_BINARY="/opt/homebrew/bin/node"
else
    # Fallback - let the system try to find it
    export NODE_BINARY=$(command -v node)
fi
